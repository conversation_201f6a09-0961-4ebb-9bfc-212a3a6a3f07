'use client';

import { XCircleIcon } from '@heroicons/react/24/outline';
import classNames from 'classnames';
import { useRouter, useSearchParams } from 'next/navigation';
import React from 'react';
import { useActivitiesLayout } from '../ActivityLayoutContext';

export default function ActivitiesLayout({ children }: { children: React.ReactNode }) {
    const searchParams = useSearchParams();
    const router = useRouter();
    const { isRightPanelExpanded, setIsRightPanelExpanded } = useActivitiesLayout();

    return (
        <>
            <span className="absolute top-[15px] z-20">
                <XCircleIcon
                    className="w-6 h-6 cursor-pointer"
                    onClick={() => {
                        setIsRightPanelExpanded(false);
                        router.push(
                            `/investors/activities${searchParams.size > 0 ? `?${searchParams.toString()}` : ''}`
                        );
                    }}
                />
            </span>

            <div
                className={classNames(
                    isRightPanelExpanded ? 'w-[32rem]' : 'w-0',
                    'top-[60px] bottom-0 right-0 overflow-y-scroll fixed border-l border-gray-300'
                )}
                style={{ boxShadow: '-4px 0 6px -1px rgba(0,0,0,0.1)' }}>
                {children}
            </div>
        </>
    );
}
