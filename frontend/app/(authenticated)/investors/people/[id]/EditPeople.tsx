import { TextField } from '@/components/forms/fields';
import { PaperClipIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { Author } from '@quarterback/types';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';

const accept =
    'image/*,text/*,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,application/vnd.ms-access,application/vnd.openxmlformats-officedocument.presentationml.slideshow,application/vnd.openxmlformats-officedocument.presentationml.template,application/vnd.openxmlformats-officedocument.wordprocessingml.template,application/vnd.openxmlformats-officedocument.spreadsheetml.template,application/vnd.oasis.opendocument.text,application/vnd.oasis.opendocument.spreadsheet,application/vnd.oasis.opendocument.presentation,application/rtf,text/csv,application/csv,application/x-csv,application/vnd.canva.presentation,application/epub+zip,application/x-iwork-keynote-sffkey,application/x-iwork-pages-sffpages,application/x-iwork-numbers-sffnumbers,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.accdb,.txt,.csv,.rtf,.odt,.ods,.odp,.pdf,.epub,.key,.pages,.numbers';
const maxSize = 10;

type NullableString = string | null | undefined;

interface FormData {
    name: NullableString;
    username: NullableString;
    profileUrl: NullableString;
    notes: NullableString;
}

export default function EditPeople({ author }: { author: Author }) {
    const [formData, setFormData] = useState<FormData>({
        name: '',
        username: '',
        profileUrl: '',
        notes: ''
    });
    const [file, setFile] = useState<File | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);

    useEffect(() => {
        if (!author) return;
        setFormData({
            name: author?.name,
            username: author?.userId,
            profileUrl: author?.url,
            notes: ''
        });
    }, [author]);
    const updateField = (title: string, value: string) => {
        setFormData({
            ...formData,
            [title]: value
        });
    };

    const validateFile = useCallback(
        (file: File): boolean => {
            if (file.size > maxSize * 1024 * 1024) {
                setError(`File size exceeds ${maxSize}MB limit`);
                return false;
            }

            // Check file type if accept is specified
            if (accept) {
                const acceptTypes = accept.split(',');
                const fileType = file.type;

                // Check if the file type matches any of the accepted types
                const isAccepted = acceptTypes.some((type) => {
                    if (type.includes('*')) {
                        const typePrefix = type.split('/')[0];
                        return fileType.startsWith(`${typePrefix}/`);
                    }
                    return type === fileType;
                });

                if (!isAccepted) {
                    setError('File type not accepted');
                    return false;
                }
            }

            setError(null);
            return true;
        },
        [maxSize, accept]
    );
    const handleFileInputChange = useCallback(
        (event: React.ChangeEvent<HTMLInputElement>) => {
            const selectedFiles = Array.from(event.target.files || []);
            const file = selectedFiles[0];

            if (file && validateFile(file)) {
                setFile(file);

                if (file.type.startsWith('image/')) {
                    const url = URL.createObjectURL(file);
                    setPreviewUrl(url);
                } else {
                    setPreviewUrl(null);
                }
            }
        },
        [setFile, validateFile, file]
    );

    const handleRemoveImage = () => {
        setFile(null);
        setPreviewUrl(null);
    };

    const resetForm = () => {
        setFile(null);
        setPreviewUrl(null);
        if (!author) return;
        setFormData({
            name: author?.name,
            username: author?.userId,
            profileUrl: author?.url,
            notes: ''
        });
    };

    const handleBlur = (field: string) => {};

    return (
        <div className="space-y-4 ">
            <TextField
                label="Name"
                value={formData?.name ?? ''}
                onChange={(value) => updateField('name', value)}
                onBlur={() => handleBlur('name')}
                placeholder="Set Name"
                // error={getFieldError('name')}
            />
            <TextField
                label="Username"
                value={formData?.username ?? ''}
                onChange={(value) => updateField('username', value)}
                onBlur={() => handleBlur('username')}
                placeholder="Set Username"
                // error={getFieldError('username')}
            />{' '}
            <TextField
                label="Profile URL"
                value={formData?.profileUrl ?? ''}
                onChange={(value) => updateField('profileUrl', value)}
                onBlur={() => handleBlur('profileUrl')}
                placeholder="Set Profile URL"
                // error={getFieldError('profileUrl')}
            />{' '}
            <TextField
                label="Notes"
                value={formData?.notes ?? ''}
                onChange={(value) => updateField('notes', value)}
                onBlur={() => handleBlur('notes')}
                placeholder="Notes"
                // error={getFieldError('notes')}
            />
            <div>
                <label className="block text-sm font-medium text-qb-gray-150 mb-1">
                    Profile Picture
                </label>
                {!file && (
                    <label
                        htmlFor="file-upload"
                        className="inline-flex cursor-pointer items-center rounded-md bg-white font-medium">
                        <span className="rounded-md border border-qb-gray-105 px-2 py-1 text-qb-gray-150 flex items-center gap-1">
                            <PaperClipIcon className="size-4" />
                            Upload an image or file
                        </span>
                        <input
                            id="file-upload"
                            name="file-upload"
                            type="file"
                            className="sr-only"
                            multiple={false}
                            onChange={handleFileInputChange}
                        />
                    </label>
                )}
                {previewUrl && (
                    <div>
                        <div className="relative mt-2 inline-block">
                            <Image
                                src={previewUrl}
                                alt="Preview"
                                className="max-h-40 rounded border border-qb-gray-100"
                            />
                            <button
                                type="button"
                                onClick={handleRemoveImage}
                                className="absolute top-1 right-1 rounded-full bg-white border border-gray-300 p-1"
                                aria-label="Remove image">
                                <XMarkIcon className="h-4 w-4 text-gray-600 text-red-600" />
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
