import ActivityDetailsCard from '@/components/ActivityDetailsCard';
import { Activity } from '@quarterback/types';
import { useMemo } from 'react';
import Stats from './Stats';

export default function PeopleActivities({
    params,
    isDetailExpanded,
    setIsDetailExpanded,
    activities: groupedActivities
}: {
    params: { id: string };
    isDetailExpanded: boolean;
    setIsDetailExpanded: (value: boolean) => void;
    activities: {
        name: string;
        datetime: Date; // Date in ISO format
        activities: Activity[];
    }[];
}) {
    const activitiesList = useMemo(() => {
        if (!groupedActivities) return [];
        return groupedActivities.flatMap((it) => it.activities);
    }, [groupedActivities]);

    return (
        <>
            <div className="flex-1 overflow-y-auto overflow-x-hidden px-4  bg-gray-50">
                <div className="flex flex-col justify-between h-full">
                    <div className="flex flex-col flex-1">
                        <Stats
                            params={params}
                            isDetailExpanded={isDetailExpanded}
                            setIsDetailExpanded={setIsDetailExpanded}
                        />

                        {/* activity lists */}
                        <div className="mt-1 flex flex-col ">
                            {activitiesList.map((it) => (
                                <ActivityDetailsCard
                                    key={it.id}
                                    activity={it}
                                    imgClassNames="-mx-8"
                                    className="border border-qb-gray-115 rounded-md my-2"
                                    show={{
                                        title: false,
                                        disclosure: false,
                                        format: false
                                    }}
                                />
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
