'use client';

import Button from '@/components/ui/Button';
import { usePeopleLayout } from '../PeopleLayoutContext';
import { useMemo, useState } from 'react';
import { Bars4Icon, ChatBubbleLeftEllipsisIcon } from '@heroicons/react/24/outline';
import Initials from '@/components/Initials';
import InitialColors from '@/util/InitialColors';
import DetailTab from './Details';
import Comments from './Comments';
import PeopleActivities from './PeopleActivities';

// TODO : ADD Comments tab once implemented

const TABS = {
    DETAILS: 'Details',
    ACTIVITIES: 'Activities'
    // COMMENTS: 'Comments'
};

const TABS_OPTIONS = [
    { name: TABS.DETAILS, icon: <Bars4Icon className="size-4" /> },
    {
        name: TABS.ACTIVITIES,
        icon: <Initials name="Activities" color={InitialColors.ACTIVITY} />
    }
    // {
    //     name: TABS.COMMENTS,
    //     icon: <ChatBubbleLeftEllipsisIcon className="size-4" />
    // }
];
export default function PersonDetailPage({ params }: { params: { id: string } }) {
    const [isDetailExpanded, setIsDetailExpanded] = useState(true);
    const [activeTab, setActiveTab] = useState(TABS.DETAILS);
    const { activitiesByPeople } = usePeopleLayout();

    const personData = useMemo(() => {
        const id = decodeURIComponent(params.id);
        console.log('personid', id);
        return activitiesByPeople.find((p) => p.author.key === id);
    }, [activitiesByPeople, params.id]);

    const props = {
        params,
        isDetailExpanded,
        setIsDetailExpanded
    };

    return (
        <div className="flex flex-col  h-full">
            <div className="border-b border-gray-200 bg-white sticky top-0 z-10">
                <div className="flex px-4 text-sm font-medium h-[52px] items-stretch pt-2">
                    {TABS_OPTIONS.map((tab) => {
                        return (
                            <Button
                                key={tab.name}
                                variant="tertiary"
                                size="sm"
                                active={activeTab === tab.name}
                                onClick={() => setActiveTab(tab.name)}>
                                <div className="flex items-center gap-1">
                                    {tab.icon}
                                    {tab.name}
                                </div>
                            </Button>
                        );
                    })}
                </div>
            </div>

            {
                activeTab === TABS.DETAILS ? (
                    <DetailTab {...props} author={personData?.author} />
                ) : activeTab === TABS.ACTIVITIES ? (
                    <PeopleActivities {...props} activities={personData?.group ?? []} />
                ) : null
                // <Comments {...props} />
            }
        </div>
    );
}
