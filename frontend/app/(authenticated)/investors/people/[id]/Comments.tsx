import Stats from './Stats';

export default function Comments({
    params,
    isDetailExpanded,
    setIsDetailExpanded
}: {
    params: { id: string };
    isDetailExpanded: boolean;
    setIsDetailExpanded: (value: boolean) => void;
}) {
    return (
        <>
            <div className="flex-1 overflow-y-auto overflow-x-hidden px-4 bg-gray-50">
                <div className="flex flex-col justify-between h-full">
                    <div className="flex flex-col flex-1">
                        <Stats
                            params={params}
                            isDetailExpanded={isDetailExpanded}
                            setIsDetailExpanded={setIsDetailExpanded}
                        />

                        {/* activity detail card */}
                        <div className="mt-1 flex flex-col ">comments list</div>
                    </div>
                </div>
            </div>
            {/* Sticky Footer */}
            <div className="bg-white border-t border-gray-200 sticky bottom-0 px-4 py-2 text-sm text-qb-gray-150">
                footer
            </div>
        </>
    );
}
