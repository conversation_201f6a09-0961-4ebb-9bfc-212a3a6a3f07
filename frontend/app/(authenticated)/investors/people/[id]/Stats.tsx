import classNames from 'classnames';
import {
    CalendarDaysIcon,
    ChevronDownIcon,
    ChevronRightIcon,
    CurrencyDollarIcon,
    FaceFrownIcon,
    FaceSmileIcon,
    HashtagIcon,
    PercentBadgeIcon
} from '@heroicons/react/24/outline';
import { useMemo } from 'react';
import { useSentimentBands } from '@/util/sentiment';
import HotcopperStatus from '@/components/HotcopperStatus';
import { formatWithTimeZone } from '@/util/date';
import { usePeopleLayout } from '../PeopleLayoutContext';

function PriceIndicator({ value }: { value: number }) {
    return (
        <span
            className={classNames(
                'text-xs inline-flex items-center rounded-md font-medium px-2 py-1 ring-1 ring-inset ring-gray-300',
                {
                    'bg-red-100 text-red-600': value && value < 0,
                    'bg-green-100 text-green-600': value && value > 0
                }
            )}>
            {Math.abs(value ?? 0).toFixed(2)}%
        </span>
    );
}

export default function Stats({
    params,
    isDetailExpanded,
    setIsDetailExpanded
}: {
    params: { id: string };
    isDetailExpanded: boolean;
    setIsDetailExpanded: (value: boolean) => void;
}) {
    const { activitiesByPeople, getAveragePriceChangeOfAuthor, authorChatters } =
        usePeopleLayout();
    const personData = useMemo(() => {
        const id = decodeURIComponent(params.id);
        return activitiesByPeople.find((p) => p.author.key === id);
    }, [activitiesByPeople, params.id]);

    const allChatters = useMemo(
        () => authorChatters(params.id),
        [authorChatters, params.id]
    );

    const averagePercentChange = useMemo(() => {
        return getAveragePriceChangeOfAuthor(params.id);
    }, [getAveragePriceChangeOfAuthor, params.id]);

    const sentimentBands = useSentimentBands(allChatters);

    const avg = getAveragePriceChangeOfAuthor(params.id);

    const expandDetailCard = () => setIsDetailExpanded(true);
    const collapseDetailCard = () => setIsDetailExpanded(false);

    const stats = useMemo(() => {
        if (!personData) return [];

        return [
            [
                'Average Sentiment',
                `${personData.averageSentiment.toFixed(2)}`,
                undefined,
                personData.averageSentiment > 0 ? (
                    <FaceSmileIcon
                        key="average-sentiment-smile"
                        className="h-4 w-4 text-gray-500"
                    />
                ) : (
                    <FaceFrownIcon
                        key="average-sentiment-frown"
                        className="h-4 w-4 text-gray-500"
                    />
                )
            ] as [string, string, undefined, JSX.Element],

            [
                'Activities',
                `${personData.activitiesCount}`,
                undefined,
                <HashtagIcon key="activities-count" className="h-4 w-4 text-gray-500" />
            ] as [string, string, undefined, JSX.Element],

            [
                'Last Activity Date',
                `${formatWithTimeZone(personData.latestDate, 'd MMM yyyy')}`,
                undefined,
                <CalendarDaysIcon key="calendar-icon" className="h-4 w-4 text-gray-500" />
            ] as [string, string, undefined, JSX.Element],

            [
                'Stock Held',
                personData.lastActivity?.type === 'hotcopper' ? (
                    <HotcopperStatus activity={personData.lastActivity} />
                ) : (
                    ''
                ),
                undefined,
                <CurrencyDollarIcon key="stock-held" className="h-4 w-4 text-gray-500" />
            ] as [string, JSX.Element | string, undefined, JSX.Element],

            [
                'Average Price Change',
                <PriceIndicator key="avg-price-indicator" value={averagePercentChange} />,
                undefined,
                <PercentBadgeIcon
                    key="average-price-change"
                    className="h-4 w-4 text-gray-500"
                />
            ] as [string, JSX.Element, undefined, JSX.Element]
        ];
    }, [personData, averagePercentChange]);
    return (
        <div
            className={classNames(
                true ? 'flex flex-col' : 'inline-flex',
                'mt-1 gap-y-2 border-b border-gray-200 py-4'
            )}>
            <div className="text-sm font-semibold flex">
                <span className="flex items-center">
                    {isDetailExpanded ? (
                        <ChevronDownIcon
                            className="h-4 w-4 mr-2 cursor-pointer"
                            onClick={collapseDetailCard}
                        />
                    ) : (
                        <ChevronRightIcon
                            className="h-4 w-4 mr-2 cursor-pointer"
                            onClick={expandDetailCard}
                        />
                    )}{' '}
                    {personData?.author?.name}
                </span>
            </div>
            <div className="overflow-hidden flex rounded-md">
                <div
                    className="h-2 bg-red-500"
                    style={{
                        width: `${(sentimentBands[0] * 100).toFixed(2)}%`
                    }}
                />
                <div
                    className="h-2 bg-yellow-300"
                    style={{
                        width: `${(sentimentBands[1] * 100).toFixed(2)}%`
                    }}
                />
                <div
                    className="h-2 bg-slate-300"
                    style={{
                        width: `${(sentimentBands[2] * 100).toFixed(2)}%`
                    }}
                />
                <div
                    className="h-2 bg-green-400"
                    style={{
                        width: `${(sentimentBands[3] * 100).toFixed(2)}%`
                    }}
                />
            </div>
            {isDetailExpanded && (
                <>
                    <dl>
                        {stats.length > 0 ? (
                            stats.map(([label, value, changeIndicator, icon]) => (
                                <div key={label} className="py-2 flex sm:col-span-1">
                                    {/* Left Column: Icon + Label */}
                                    <div className="text-sm font-medium text-gray-900 flex items-center gap-2 w-1/2">
                                        {icon && (
                                            <div className="text-qb-gray-150">{icon}</div>
                                        )}
                                        <dt className="text-sm font-medium text-qb-gray-150">
                                            {label}
                                        </dt>
                                    </div>

                                    {/* Right Column: Value + Optional Change Indicator */}
                                    <dd className="w-1/2 text-sm text-qb-black-100 flex items-center gap-2">
                                        <span>{value}</span>
                                        {changeIndicator}
                                    </dd>
                                </div>
                            ))
                        ) : (
                            <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                                <dt className="text-sm font-medium text-gray-900">
                                    Market closed
                                </dt>
                            </div>
                        )}
                    </dl>
                </>
            )}
        </div>
    );
}
