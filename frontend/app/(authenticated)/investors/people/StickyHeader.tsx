'use client';

import DateRangePicker from '@/components/DateRangePicker';
import Button from '@/components/ui/Button';
import Dropdown, { DropdownOption } from '@/components/ui/Dropdown';
import {
    BarsArrowDownIcon,
    BarsArrowUpIcon,
    CloudArrowDownIcon,
    EllipsisVerticalIcon,
    FlagIcon,
    PencilSquareIcon,
    PlusIcon,
    TrashIcon
} from '@heroicons/react/24/outline';
import { HotCopperPost } from '@quarterback/types';
import { download, generateCsv, mkConfig } from 'export-to-csv';
import { DateRange } from 'react-day-picker';
import { ActivityByPeople } from './PeopleLayoutContext';
import { formatInTimeZone } from 'date-fns-tz';
import { format } from 'date-fns';

export const SORT_OPTIONS: DropdownOption[] = [
    { name: 'Average Sentiment', value: 'avgSentiment' },
    { name: 'Last Activity Date', value: 'lastActivityDate' },
    { name: 'Activities', value: 'mostActivities' }
];

export const SORT_ORDER = {
    ASC: 'asc',
    DESC: 'desc'
};
export type SortOption = (typeof SORT_OPTIONS)[number];
type SortOrder = keyof typeof SORT_ORDER;
export type SortOrderValue = (typeof SORT_ORDER)[SortOrder];

export default function PeopleStickyHeader({
    selectedSort,
    setSelectedSort,
    sortOrder,
    setSortOrder,
    selectedPeople,
    range,
    setRange,
    activities
}: {
    selectedSort?: string;
    sortOrder?: SortOrderValue;
    setSelectedSort: (value: string) => void;
    setSortOrder: (value: SortOrderValue) => void;
    selectedPeople: string[];
    range: DateRange;
    setRange: (range: DateRange) => void;
    activities: ActivityByPeople[];
}) {
    function exportCSV() {
        function disclosure(activity: HotCopperPost) {
            switch (activity?.hotcopper?.disclosure) {
                case 'HELD':
                    return 'Held';
                case 'NOT_HELD':
                    return 'Not held';
                case 'UNDISCLOSED':
                default:
                    return 'Undisclosed';
            }
        }
        const startStr = range.from ? format(range.from, 'd MMM yyyy') : 'start';
        const endStr = range.to ? format(range.to, 'd MMM yyyy') : 'end';
        const now = format(new Date(), 'd MMM yyyy');
        const slugify = (str: string) => str.replace(/[^\w\d]/g, '-');

        const config = mkConfig({
            filename: slugify(`people-reports_${startStr}_to_${endStr}_${now}`),
            showColumnHeaders: true,
            columnHeaders: [
                { key: 'person', displayLabel: 'Person' },
                { key: 'avgSentiment', displayLabel: 'Average Sentiment' },
                { key: 'activities', displayLabel: 'Activities' },
                { key: 'lastActivityDate', displayLabel: 'Last Activity Date' },
                { key: 'stockHeld', displayLabel: 'Stock Held' }
            ]
        });

        const csv = generateCsv(config)(
            activities.map((it) => ({
                person: it.author.name,
                avgSentiment: it.averageSentiment.toFixed(2),
                activities: it.activitiesCount,
                lastActivityDate: formatInTimeZone(
                    it.latestDate,
                    'Australia/Sydney',
                    'd MMMM yyyy'
                ),
                stockHeld:
                    it.lastActivity?.type === 'hotcopper'
                        ? disclosure(it.lastActivity)
                        : ''
            }))
        );
        download(config)(csv);
    }
    return (
        <div className="bg-white divide-y divide-gray-200 sticky top-0 z-10 border-b bottom-gray-200">
            <div className="flex justify-end px-4 py-2">
                <DateRangePicker range={range} setRange={setRange} required />
            </div>
            <div className="sm:flex sm:items-center px-4 py-2">
                <div className="flex flex-row gap-x-2 justify-between items-center  w-full">
                    <div className="flex items-center">
                        <Dropdown
                            options={SORT_OPTIONS}
                            selected={selectedSort}
                            icon={
                                sortOrder === SORT_ORDER.ASC
                                    ? BarsArrowUpIcon
                                    : BarsArrowDownIcon
                            }
                            onChange={setSelectedSort}
                            title={selectedSort ? 'Sorted by' : 'Sort by'}
                            buttonClass={selectedSort ? 'rounded-r-none bg-white' : ''}
                            borderDashed
                        />
                        {selectedSort && (
                            <>
                                <Dropdown
                                    options={[
                                        { name: 'ascending', value: SORT_ORDER.ASC },
                                        { name: 'decending', value: SORT_ORDER.DESC }
                                    ]}
                                    selected={sortOrder}
                                    onChange={setSortOrder}
                                    buttonClass={'rounded-l-none rounded-r-none'}
                                    borderDashed
                                />
                                <Dropdown
                                    options={[
                                        {
                                            name: (
                                                <span className="flex items-center gap-1 text-red-600">
                                                    {' '}
                                                    <TrashIcon className="h-4 w-4" />
                                                    Clear
                                                </span>
                                            ),
                                            value: 'clear'
                                        }
                                    ]}
                                    selected={sortOrder}
                                    onChange={() => setSelectedSort('')}
                                    buttonClass="rounded-l-none border-l-0"
                                    placeholder={
                                        <span className="flex">
                                            <EllipsisVerticalIcon className="h-5 w-5" />
                                        </span>
                                    }
                                    borderDashed
                                />
                            </>
                        )}
                    </div>
                    <div>
                        <div className="flex items-stretch flex-nowrap">
                            {/* Left Box */}
                            <div className="rounded-l-md border border-gray-200 px-2 flex items-center gap-1 h-full">
                                {selectedPeople?.length ? (
                                    <span className="inline-flex items-center justify-center px-2 py-0.5 text-xs font-medium bg-indigo-700 text-white rounded-md">
                                        {selectedPeople.length}
                                    </span>
                                ) : null}
                                Selected
                            </div>

                            {/* Middle Icon Box */}

                            <div className="border border-gray-200 items-center flex px-2">
                                <FlagIcon className="size-4" />
                            </div>
                            <div className="rounded-r-md border border-gray-200 items-center flex px-2">
                                <PencilSquareIcon className="size-4" />
                            </div>
                        </div>
                    </div>
                    <div className="flex gap-2">
                        <Button
                            variant="secondary"
                            onClick={exportCSV}
                            size="sm"
                            icon={<CloudArrowDownIcon className="size-4" />}>
                            Export CSV
                        </Button>
                        <Button
                            variant="primary"
                            size="sm"
                            icon={<PlusIcon className="size-4" />}>
                            New Person
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
}
