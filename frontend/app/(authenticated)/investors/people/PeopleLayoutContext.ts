import { DateRange } from 'react-day-picker';
import { SortOrderValue } from './StickyHeader';
import { Activity, Author } from '@quarterback/types';
import { createContext, useContext } from 'react';
import { TimeSeriesQuote } from '@/api/hooks/useTimeSeries';

export type ActivityByPeople = {
    author: Author;
    group: {
        name: string;
        datetime: Date; // Date in ISO format
        activities: Activity[];
    }[];
    activitiesCount: number;
    averageSentiment: number;
    latestDate: Date;
    lastActivity: Activity;
};
interface PeopleLayoutContextType {
    range: DateRange;
    setRange: (range: DateRange) => void;
    selectedSort: string | undefined;
    setSelectedSort: (sort: string | undefined) => void;
    sortOrder: SortOrderValue;
    setSortOrder: (order: SortOrderValue) => void;
    selectedPeople: string[];
    setSelectedPeople: React.Dispatch<React.SetStateAction<string[]>>;
    activitiesByPeople: ActivityByPeople[];
    timeSeriesByDate: { [key: string]: TimeSeriesQuote[] } | undefined;
    getAveragePriceChangeOfAuthor: (value: string) => number;
    authorChatters: (value: string) => Array<Activity>;
}

const PeopleLayoutContext = createContext<PeopleLayoutContextType | null>(null);

export function usePeopleLayout() {
    const context = useContext(PeopleLayoutContext);
    if (!context) {
        throw new Error('usePeopleLayout must be used within a PeopleLayoutProvider');
    }
    return context;
}

export default PeopleLayoutContext;
