import useActivities from '@/api/hooks/useActivities';
import { isChatter } from '@/api/hooks/useChatter';
import useTimeSeries from '@/api/hooks/useTimeSeries';
import { useOrganisation } from '@/components/OrganisationProvider';
import { formatWithTimeZone } from '@/util/date';
import hasField from '@/util/hasField';
import { sentimentScore, useSentimentBands } from '@/util/sentiment';
import { Activity } from '@quarterback/types';
import { groupBy } from '@quarterback/util';
import { isBefore, subMonths } from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';
import { useMemo } from 'react';
import { DateRange } from 'react-day-picker';

function usePeopleStats(range: DateRange) {
    const organisation = useOrganisation();
    const { data: activities = [], isLoading: activitiesLoading } = useActivities(
        organisation.selected?.organisation,
        organisation.selected?.entity,
        range.from!,
        range.to!
    );

    const { data: timeSeries, isLoading: timeSeriesLoading } = useTimeSeries(
        organisation?.selected?.entity,
        range.from!,
        range.to!
    );

    const timeSeriesByDate = useMemo(() => {
        return groupBy(timeSeries?.values ?? [], (it) =>
            formatInTimeZone(new Date(it.datetime), 'Australia/Sydney', 'yyyy-MM-dd')
        );
    }, [timeSeries]);

    const calculateAverageSentiment = (activities: Activity[]) => {
        const agg = [...activities].filter(hasField('sentiment')).reduce(
            (agg, activity) => ({
                count: agg.count + 1,
                sum: agg.sum + sentimentScore(activity.sentiment)
            }),
            { count: 0, sum: 0 }
        );
        return agg.count > 0 ? agg.sum / agg.count : 0;
    };
    const activitiesByPeople = useMemo(() => {
        const safeActivities = Array.isArray(activities) ? activities : [];
        const validActivities = safeActivities.filter(
            (activity): activity is typeof activity & { author: { key: string } } =>
                !!activity &&
                activity.type !== 'asx-announcement' &&
                !!activity.author?.key
        );

        return Object.entries(groupBy(validActivities, (it) => it.author.key)).map(
            ([authorKey, activities]) => {
                const author = activities[0]?.author;
                // const lastActivity = Math.max(...activities.map(a => new Date(a.posted).getTime()))

                const lastActivity = activities.reduce((latest, activity) =>
                    new Date(activity.posted) > new Date(latest.posted)
                        ? activity
                        : latest
                );
                const activitiesByDay = groupBy(activities, (it) =>
                    formatInTimeZone(it.posted!, 'Australia/Sydney', 'yyyy-MM-dd')
                );
                const groupedActivities = Object.keys(activitiesByDay)
                    .map((group) => {
                        const date = new Date(group);
                        return {
                            name: formatInTimeZone(
                                date,
                                'Australia/Sydney',
                                isBefore(date, subMonths(new Date(), 12))
                                    ? 'MMM do, yyyy'
                                    : 'MMM do'
                            ),
                            datetime: date,
                            activities: activitiesByDay[group].sort(
                                (a, b) => b.posted.valueOf() - a.posted.valueOf()
                            )
                        };
                    })
                    .sort((a, b) => b.datetime.valueOf() - a.datetime.valueOf());
                return {
                    author,
                    group: groupedActivities,
                    activitiesCount: activities.length,
                    averageSentiment: calculateAverageSentiment(activities),
                    latestDate: new Date(lastActivity?.posted),
                    lastActivity
                };
            }
        );
    }, [activities]);

    const getAuthorActivitiesGroup = (author: string) => {
        return activitiesByPeople.find((act) => act?.author?.key === author)?.group;
    };

    const authorChatters = (author: string) => {
        const authorActivities = getAuthorActivitiesGroup(author);
        if (!authorActivities || authorActivities === undefined) return [];
        const activityDates = [];
        const chatterActivities: Array<Activity> = [];

        authorActivities.map((group) => {
            const activityDate = formatWithTimeZone(group.datetime, 'yyyy-MM-dd');
            activityDates.push(activityDate);
            chatterActivities.push(...group.activities.filter((it) => isChatter(it)));
        });
        return chatterActivities;
    };

    const getAveragePriceChangeOfAuthor = (author: string) => {
        const percentChanges: number[] = [];
        const authorActivities = getAuthorActivitiesGroup(author);
        if (!authorActivities || authorActivities === undefined) return 0;
        for (const group of authorActivities) {
            const activityDate = formatWithTimeZone(group.datetime, 'yyyy-MM-dd');

            const dailyData = timeSeriesByDate[activityDate];
            if (dailyData && dailyData.length > 0) {
                const { close, open } = dailyData[0];
                const percent = ((close - open) * 100) / open;
                console.log({ close, open, percent });
                percentChanges.push(percent);
            }
        }

        if (percentChanges.length === 0) return 0;

        const averagePercentChange =
            percentChanges.length > 0
                ? percentChanges.reduce((sum, value) => sum + value, 0) /
                  percentChanges.length
                : 0;
        return averagePercentChange;
    };
    return {
        activitiesByPeople,
        loading: activitiesLoading,
        timeSeriesByDate,
        getAveragePriceChangeOfAuthor,
        authorChatters
    };
}

export default usePeopleStats;
