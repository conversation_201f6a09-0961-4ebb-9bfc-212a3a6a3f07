import { TextFieldProps } from '@quarterback/types';
import classNames from 'classnames';

export function TextField({
    label,
    value,
    onChange,
    onBlur,
    placeholder,
    min,
    max,
    step,
    error,
    readOnly = false,
    required = false,
    type = 'text'
}: TextFieldProps) {
    return (
        <div>
            <label className="block text-sm font-medium text-qb-gray-150 mb-1">
                {label}
                {required && <span className="text-indigo-500">*</span>}
            </label>
            <input
                type={type}
                readOnly={readOnly}
                className={classNames(
                    'block w-full rounded-md sm:text-sm placeholder-qb-gray-150',
                    error
                        ? 'border-red-300 text-red-900 focus:border-red-500 focus:ring-red-500'
                        : 'border-gray-300 focus:border-indigo-500 focus:ring-indigo-500'
                )}
                value={value}
                min={min}
                max={max}
                step={step}
                onChange={(e) => {
                    onChange(e.target.value);
                }}
                onBlur={onBlur}
                placeholder={placeholder}
                required={required}
            />
            {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
        </div>
    );
}
