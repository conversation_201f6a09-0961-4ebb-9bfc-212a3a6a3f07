import { TextAreaProps } from '@quarterback/types';
import classNames from 'classnames';

export function TextArea({
    label,
    value,
    onChange,
    onBlur,
    rows = 4,
    placeholder,
    error,
    required = false
}: TextAreaProps) {
    return (
        <div>
            <label className="block text-sm font-medium text-qb-gray-150 mb-1">
                {label}
                {required && <span className="text-red-500">*</span>}
            </label>
            <textarea
                className={classNames(
                    'block w-full rounded-md sm:text-sm placeholder-qb-gray-150',
                    error
                        ? 'border-red-300 text-red-900 focus:border-red-500 focus:ring-red-500'
                        : 'border-gray-300 focus:border-indigo-500 focus:ring-indigo-500'
                )}
                rows={rows}
                value={value}
                onChange={(e) => {
                    onChange(e.target.value);
                }}
                onBlur={onBlur}
                placeholder={placeholder}
                required={required}
            />
            {error && <p className="mt-2 text-sm text-red-600">{error}</p>}
        </div>
    );
}
