import { HotCopperPost } from '@quarterback/types';
import { useMemo } from 'react';

export default function HotCopperStatus({ activity }: { activity: HotCopperPost }) {
    const disclosure = useMemo(() => {
        switch (activity.hotcopper?.disclosure) {
            case 'HELD':
                return 'Held';
            case 'NOT_HELD':
                return 'Not held';
            case 'UNDISCLOSED':
            default:
                return 'Undisclosed';
        }
    }, [activity]);

    return (
        <span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
            {disclosure}
        </span>
    );
}
